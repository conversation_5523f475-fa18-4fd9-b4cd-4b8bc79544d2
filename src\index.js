import express from "express";
import multer from "multer";
import CVGenerator from "./generator.js";
import { authenticate<PERSON><PERSON><PERSON><PERSON> } from "./middleware/auth.js";
import path from "path";
import fs from "fs/promises";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const upload = multer({ dest: "uploads/" });
const generator = new CVGenerator();

app.use(express.json());

// Health check - protected with API key
app.get("/health", authenticateApiKey, (req, res) => {
  res.json({ status: "ok", message: "CV Generator API is running" });
});

// Generate CV endpoint - protected with API key
app.post(
  "/generate",
  authenticateApiKey,
  upload.fields([
    { name: "markdown", maxCount: 1 },
    { name: "css", maxCount: 1 },
  ]),
  async (req, res) => {
    try {
      const markdownFile = req.files["markdown"][0];
      const cssFile = req.files["css"][0];

      const outputPath = path.join("outputs", `cv-${Date.now()}.pdf`);

      // Ensure output directory exists
      await fs.mkdir("outputs", { recursive: true });

      // Parse options from request body
      const options = {
        fontSize: req.body.fontSize || "12px",
        lineHeight: parseFloat(req.body.lineHeight) || 1.3,
        marginTop: req.body.marginTop || "70px",
        marginBottom: req.body.marginBottom || "70px",
        marginLeft: req.body.marginLeft || "45px",
        marginRight: req.body.marginRight || "45px",
      };

      // Generate PDF
      await generator.generatePDF(
        markdownFile.path,
        cssFile.path,
        outputPath,
        options
      );

      // Send PDF file
      res.download(outputPath, "cv.pdf", async (err) => {
        // Cleanup uploaded files
        await fs.unlink(markdownFile.path);
        await fs.unlink(cssFile.path);

        // Cleanup generated PDF after sending
        setTimeout(async () => {
          try {
            await fs.unlink(outputPath);
          } catch (e) {
            // File might already be deleted
          }
        }, 60000); // Delete after 1 minute
      });
    } catch (error) {
      console.error("Error generating CV:", error);
      res.status(500).json({ error: error.message });
    }
  }
);

// Add a route to show API usage when accessed without authentication
app.get("/", (req, res) => {
  res.json({
    message: "CV Generator API",
    version: "1.0.0",
    endpoints: {
      "GET /health": "Health check (requires API key)",
      "POST /generate":
        "Generate CV from markdown and CSS files (requires API key)",
    },
    authentication:
      "Required for all endpoints. Use X-API-Key, Authorization (Bearer), or API-Key header",
    example:
      "curl -H 'X-API-Key: your-secret-key' http://localhost:3000/health",
  });
});

// Handle 404 for unknown routes
app.use("*", (req, res) => {
  res.status(404).json({
    error: "Not Found",
    message: "The requested endpoint does not exist",
    availableEndpoints: ["/", "/health", "/generate"],
  });
});

const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
  console.log(`CV Generator server running on port ${PORT}`);
  console.log(
    `API Key authentication ${
      process.env.API_KEY
        ? "enabled"
        : "DISABLED - Please set API_KEY environment variable"
    }`
  );
});
