import dotenv from 'dotenv';

dotenv.config();

const API_KEY = process.env.API_KEY;

export const authenticateApiKey = (req, res, next) => {
  // Check if API_KEY is configured
  if (!API_KEY) {
    console.error('API_KEY environment variable is not set');
    return res.status(500).json({ 
      error: 'Server configuration error',
      message: 'API key not configured' 
    });
  }

  // Get API key from different possible headers
  const providedKey = req.headers['x-api-key'] || 
                     req.headers['authorization']?.replace('Bearer ', '') ||
                     req.headers['api-key'];

  if (!providedKey) {
    return res.status(401).json({
      error: 'Unauthorized',
      message: 'API key is required. Provide it in X-API-Key, Authorization (Bearer), or API-Key header'
    });
  }

  // Use constant-time comparison to prevent timing attacks
  if (!constantTimeCompare(providedKey, API_KEY)) {
    return res.status(401).json({
      error: 'Unauthorized',
      message: 'Invalid API key'
    });
  }

  next();
};

// Constant-time string comparison to prevent timing attacks
function constantTimeCompare(a, b) {
  if (a.length !== b.length) {
    return false;
  }
  
  let result = 0;
  for (let i = 0; i < a.length; i++) {
    result |= a.charCodeAt(i) ^ b.charCodeAt(i);
  }
  
  return result === 0;
}