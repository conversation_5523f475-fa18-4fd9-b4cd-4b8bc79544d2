export default function handler(req, res) {
  res.json({
    message: "CV Generator API",
    version: "1.0.0",
    endpoints: {
      "GET /health": "Health check (requires API key)",
      "POST /generate": "Generate CV from markdown and CSS files (requires API key)",
    },
    authentication:
      "Required for all endpoints. Use X-API-Key, Authorization (Bearer), or API-Key header",
    example:
      "curl -H 'X-API-Key: your-secret-key' https://your-app.vercel.app/health",
  });
}