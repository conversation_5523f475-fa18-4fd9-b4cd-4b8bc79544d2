import { authenticate<PERSON><PERSON><PERSON><PERSON> } from "../src/middleware/auth.js";
import CVGenerator from "../src/generator.js";
import formidable from "formidable";
import fs from "fs/promises";
import path from "path";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Disable the default body parser
export const config = {
  api: {
    bodyParser: false,
  },
};

export default async function handler(req, res) {
  if (req.method !== "POST") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  // Apply authentication middleware
  return new Promise((resolve) => {
    authenticateApiKey(req, res, async () => {
      try {
        const generator = new CVGenerator();

        // Parse form data
        const form = formidable({
          uploadDir: "/tmp",
          keepExtensions: true,
          maxFileSize: 10 * 1024 * 1024, // 10MB
        });

        const [fields, files] = await form.parse(req);

        const markdownFile = files.markdown?.[0];
        const cssFile = files.css?.[0];

        if (!markdownFile || !cssFile) {
          return res.status(400).json({
            error: "Both markdown and css files are required",
          });
        }

        // Create output path in /tmp for serverless
        const outputPath = path.join("/tmp", `cv-${Date.now()}.pdf`);

        // Parse options from form fields
        const options = {
          fontSize: fields.fontSize?.[0] || "12px",
          lineHeight: parseFloat(fields.lineHeight?.[0]) || 1.3,
          marginTop: fields.marginTop?.[0] || "70px",
          marginBottom: fields.marginBottom?.[0] || "70px",
          marginLeft: fields.marginLeft?.[0] || "45px",
          marginRight: fields.marginRight?.[0] || "45px",
        };

        // Generate PDF
        await generator.generatePDF(
          markdownFile.filepath,
          cssFile.filepath,
          outputPath,
          options
        );

        // Read the generated PDF
        const pdfBuffer = await fs.readFile(outputPath);

        // Set headers for PDF download
        res.setHeader("Content-Type", "application/pdf");
        res.setHeader("Content-Disposition", 'attachment; filename="cv.pdf"');
        res.setHeader("Content-Length", pdfBuffer.length);

        // Send PDF
        res.status(200).send(pdfBuffer);

        // Cleanup files
        try {
          await fs.unlink(markdownFile.filepath);
          await fs.unlink(cssFile.filepath);
          await fs.unlink(outputPath);
        } catch (e) {
          console.error("Cleanup error:", e);
        }

        resolve();
      } catch (error) {
        console.error("Error generating CV:", error);
        res.status(500).json({ error: error.message });
        resolve();
      }
    });
  });
}
