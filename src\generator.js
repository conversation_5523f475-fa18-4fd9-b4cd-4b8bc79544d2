// Dynamic imports for serverless compatibility
let puppeteer;
let chromium;

// Check if we're running in production (Vercel) or development
const isProduction =
  process.env.NODE_ENV === "production" || process.env.VERCEL;

if (isProduction) {
  // Use serverless-compatible packages in production
  puppeteer = (await import("puppeteer-core")).default;
  chromium = (await import("@sparticuz/chromium")).default;
} else {
  // Use full puppeteer in development
  puppeteer = (await import("puppeteer")).default;
}

import MarkdownIt from "markdown-it";
import matter from "gray-matter";
import fs from "fs/promises";
import path from "path";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class CVGenerator {
  constructor() {
    this.md = new MarkdownIt({
      html: true,
      linkify: true,
      typographer: true,
    });
  }

  async generatePDF(markdownPath, cssPath, outputPath, options = {}) {
    try {
      // Read markdown file
      const markdownContent = await fs.readFile(markdownPath, "utf-8");

      // Parse frontmatter and content
      const { data: frontmatter, content } = matter(markdownContent);

      // Generate HTML
      const html = await this.generateHTML(
        frontmatter,
        content,
        cssPath,
        options,
      );

      // Convert to PDF
      await this.htmlToPDF(html, outputPath, options);

      return outputPath;
    } catch (error) {
      throw new Error(`Failed to generate PDF: ${error.message}`);
    }
  }

  async generateHTML(frontmatter, markdownContent, cssPath, options = {}) {
    // Parse markdown to HTML first
    const contentHTML = this.md.render(markdownContent);

    // Post-process HTML to handle ~ syntax
    const processedHTML = this.postProcessHTML(contentHTML);

    // Generate header HTML
    const headerHTML = this.generateHeader(frontmatter);

    // Read CSS
    const css = await fs.readFile(cssPath, "utf-8");

    // Read base template
    const templatePath = path.join(__dirname, "templates", "base.html");
    const template = await fs.readFile(templatePath, "utf-8");

    // Replace placeholders
    const html = template
      .replace("{{title}}", frontmatter.name || "CV")
      .replace("{{styles}}", css)
      .replace("{{additionalStyles}}", this.generateAdditionalStyles(options))
      .replace("{{header}}", headerHTML)
      .replace("{{content}}", processedHTML);

    return html;
  }

  postProcessHTML(html) {
    // Handle ~ syntax within paragraph tags
    // First regex: Match project entries with links
    const projectRegex =
      /<p>(<strong><a href="[^"]*">.*?<\/a>,<\/strong> <em>.*?<\/em>)\n~\s*([^]*?)<\/p>/g;

    // Second regex: Match job entries with company links
    const jobWithLinkRegex =
      /<p>(<strong>[^<]*?,<\/strong> <em><a href="[^"]*">.*?<\/a><\/em>)\n~\s*([^]*?)<\/p>/g;

    // Third regex: Match job entries without links
    const jobRegex =
      /<p>(<strong>[^<]*?,<\/strong> <em>[^<]*?<\/em>)\n~\s*([^]*?)<\/p>/g;

    // Fourth regex: Match education entries (just strong tag, no em)
    const educationRegex = /<p>(<strong>[^<]*?,<\/strong>)\n~\s*([^]*?)<\/p>/g;

    let processedHTML = html.replace(
      projectRegex,
      (match, titlePart, datePart) => {
        const title = titlePart.trim();
        const date = datePart.trim();

        return `<div class="project-header">
<div class="project-title">
<p>${title}</p>
</div>
<div class="project-date">${date}</div>
</div>`;
      },
    );

    processedHTML = processedHTML.replace(
      jobWithLinkRegex,
      (match, titlePart, datePart) => {
        const title = titlePart.trim();
        const date = datePart.trim();

        return `<div class="project-header">
<div class="project-title">
<p>${title}</p>
</div>
<div class="project-date">${date}</div>
</div>`;
      },
    );

    processedHTML = processedHTML.replace(
      jobRegex,
      (match, titlePart, datePart) => {
        const title = titlePart.trim();
        const date = datePart.trim();

        return `<div class="project-header">
<div class="project-title">
<p>${title}</p>
</div>
<div class="project-date">${date}</div>
</div>`;
      },
    );

    processedHTML = processedHTML.replace(
      educationRegex,
      (match, titlePart, datePart) => {
        const title = titlePart.trim();
        const date = datePart.trim();

        return `<div class="project-header">
<div class="project-title">
<p>${title}</p>
</div>
<div class="project-date">${date}</div>
</div>`;
      },
    );

    return processedHTML;
  }

  generateHeader(frontmatter) {
    if (!frontmatter.name || !frontmatter.header) {
      return "";
    }

    let headerItems = frontmatter.header
      .map((item, index) => {
        const isLast = index === frontmatter.header.length - 1;
        const className = isLast
          ? "resume-header-item no-separator"
          : "resume-header-item";

        if (item.link) {
          return `<span class="${className}">
      <a href="${item.link}" target="_blank" rel="noopener noreferrer">${item.text}</a>
    </span>`;
        } else {
          return `<span class="${className}">
      ${item.text}
    </span>`;
        }
      })
      .join("\n");

    return `<div class="resume-header"><h1>${frontmatter.name}</h1>
${headerItems}</div>`;
  }

  generateAdditionalStyles(options) {
    const {
      fontSize = "12px",
      marginTop = "20px",
      marginBottom = "20px",
      marginLeft = "25px",
      marginRight = "25px",
      lineHeight = "1",
      fontFamily = "Arial, sans-serif",
    } = options;

    return `
      <style>
        #resume-preview [data-scope="vue-smart-pages"][data-part="page"] {
          font-family: ${fontFamily};
          font-size: ${fontSize};
          line-height: ${lineHeight};
          color: black;
        }

        #resume-preview p {
          margin-bottom: 5px;
        }

        @page {
          margin: ${marginTop} ${marginRight} ${marginBottom} ${marginLeft};
        }
      </style>
    `;
  }

  async htmlToPDF(html, outputPath, options = {}) {
    let browser;

    if (isProduction) {
      // Vercel/serverless configuration
      browser = await puppeteer.launch({
        args: chromium.args,
        defaultViewport: chromium.defaultViewport,
        executablePath: await chromium.executablePath(),
        headless: chromium.headless,
        ignoreHTTPSErrors: true,
      });
    } else {
      // Local development configuration - try system Chrome if puppeteer fails
      try {
        browser = await puppeteer.launch({
          headless: "new",
          args: ["--no-sandbox", "--disable-setuid-sandbox"],
        });
      } catch (error) {
        console.log("Puppeteer Chrome failed, trying system Chrome...");
        browser = await puppeteer.launch({
          executablePath:
            "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",
          headless: "new",
          args: ["--no-sandbox", "--disable-setuid-sandbox"],
        });
      }
    }

    try {
      const page = await browser.newPage();

      // Set content
      await page.setContent(html, { waitUntil: "networkidle0" });

      // Wait for any iconify icons to load
      await page.evaluate(() => {
        return new Promise((resolve) => {
          if (typeof Iconify !== "undefined") {
            // Wait a bit for icons to render
            setTimeout(resolve, 1000);
          } else {
            resolve();
          }
        });
      });

      // Generate PDF with options
      const pdfOptions = {
        path: outputPath,
        format: "A4",
        printBackground: true,
        displayHeaderFooter: false,
        margin: {
          top: options.marginTop || "20px",
          bottom: options.marginBottom || "20px",
          left: options.marginLeft || "25px",
          right: options.marginRight || "25px",
        },
      };

      await page.pdf(pdfOptions);
    } finally {
      await browser.close();
    }
  }
}

export default CVGenerator;
